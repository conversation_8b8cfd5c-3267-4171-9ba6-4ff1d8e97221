import React from 'react'
import { createRoot } from 'react-dom/client'
import App from './App'
import './styles.css'

// Mount the React application. The root element exists in index.html.
const root = createRoot(document.getElementById('root'))
root.render(<App />)

// Prevent the browser’s context menu from interfering with drag‑and‑drop
window.addEventListener('contextmenu', (e) => {
  e.preventDefault()
})