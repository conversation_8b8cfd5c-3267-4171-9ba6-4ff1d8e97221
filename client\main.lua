--[[
    Client side entrypoint for the grid inventory system.

    This script manages user interaction on the local player. It handles
    keyboard/controller input, opens and closes the UI, forwards drag
    actions to the server, and listens for inventory updates. The default
    GTA V weapon wheel is disabled using a ped configuration flag so
    players never see the vanilla inventory. For more details on why
    this flag is used see the FiveM forum discussion【983711235337996†L97-L106】.

    The UI itself runs inside a NUI environment built with React. To
    update the UI you send messages via SendNUIMessage() with a JSON
    payload. Conversely, events from the UI are received via
    RegisterNUICallback(). See `nui/src` for the React implementation.
]]

local Config = Config
-- The utilities module is only needed on the server. On the client we
-- avoid requiring it because `require('shared/utils')` is not
-- supported in all environments. If you need helpers on the client
-- consider copying the necessary functions here.

local inventoryOpen = false
local cachedInventory = nil
local cachedHotbar = nil

-- Disable the default weapon wheel. Setting ped config flag 48 to
-- true blocks the player from switching weapons and hides the wheel
--【983711235337996†L97-L106】. We apply this on every tick to ensure it remains
-- enforced even if the ped entity changes (e.g. during respawn).
CreateThread(function()
    while true do
        Wait(1000)
        local ped = PlayerPedId()
        if DoesEntityExist(ped) then
            SetPedConfigFlag(ped, 48, true)
        end
    end
end)

-- Request the current inventory from the server and open the UI
local function openInventory()
    if inventoryOpen then return end
    inventoryOpen = true
    -- Send cached data immediately if available; otherwise request from server.
    if cachedInventory then
        SendNUIMessage({
            type = 'open',
            inventory = cachedInventory,
            hotbar = cachedHotbar,
            grid = {w = Config.GridWidth, h = Config.GridHeight}
        })
    else
        TriggerServerEvent('vantage_inventory:requestInventory')
    end
    -- Focus the NUI so it can capture keyboard/mouse input
    SetNuiFocus(true, true)
end

local function closeInventory()
    if not inventoryOpen then return end
    inventoryOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({type = 'close'})
end

-- Key mapping registration. We avoid exposing player accessible text
-- commands by using hidden commands triggered only by key bindings.
RegisterCommand('vantage_inventory:toggle', function()
    if inventoryOpen then
        closeInventory()
    else
        openInventory()
    end
end, false)
RegisterKeyMapping('vantage_inventory:toggle', 'Toggle Inventory', 'keyboard', Config.Keybinds.ToggleInventory)

-- Hotbar keys. Each key triggers a quick use on the corresponding slot
-- index. The slot index matches the order defined in Config.Keybinds.Hotbar.
for index, key in ipairs(Config.Keybinds.Hotbar) do
    local command = ('vantage_inventory:hotbar%s'):format(index)
    RegisterCommand(command, function()
        if inventoryOpen then
            -- Don’t quick use while the inventory is open; players should
            -- click instead.  Close inventory then use item.
            return
        end
        TriggerServerEvent('vantage_inventory:hotbarUse', index)
    end, false)
    RegisterKeyMapping(command, ('Use hotbar slot %s'):format(index), 'keyboard', key)
end

-- Register other keybinds (rotate, drop, give) but handle logic inside
-- NUI callbacks because they depend on the drag context.
RegisterCommand('vantage_inventory:rotate', function()
    SendNUIMessage({type = 'rotate'})
end, false)
RegisterKeyMapping('vantage_inventory:rotate', 'Rotate item during drag', 'keyboard', Config.Keybinds.Rotate)

RegisterCommand('vantage_inventory:quickdrop', function()
    SendNUIMessage({type = 'quickdrop'})
end, false)
RegisterKeyMapping('vantage_inventory:quickdrop', 'Quick drop item', 'keyboard', Config.Keybinds.QuickDrop)

RegisterCommand('vantage_inventory:quickgive', function()
    SendNUIMessage({type = 'quickgive'})
end, false)
RegisterKeyMapping('vantage_inventory:quickgive', 'Quick give item', 'keyboard', Config.Keybinds.QuickGive)

-- Receive updated inventory from the server and forward it to the UI
RegisterNetEvent('vantage_inventory:update', function(inventory, hotbar)
    cachedInventory = inventory
    cachedHotbar = hotbar
    if inventoryOpen then
        SendNUIMessage({
            type = 'open',
            inventory = inventory,
            hotbar = hotbar,
            grid = {w = Config.GridWidth, h = Config.GridHeight}
        })
    end
end)

-- Force close the inventory UI. The server can emit this event when
-- resynchronising after detecting desynchronisation or as part of an
-- anti‑dupe measure.
RegisterNetEvent('vantage_inventory:forceClose', function()
    closeInventory()
end)

-- NUI callbacks. These functions are invoked by the React UI via
-- fetch(`https://<resource_name>/<callback>`) from the browser context.
RegisterNUICallback('close', function(_, cb)
    closeInventory()
    cb('ok')
end)

-- Move or rotate an item. The UI sends the item id, destination slot and
-- optionally a split amount. The server validates the move and updates
-- the authoritative inventory state.
RegisterNUICallback('moveItem', function(data, cb)
    -- data: {itemId, toRow, toCol, splitAmount, rotate}
    TriggerServerEvent('vantage_inventory:moveItem', data)
    cb('ok')
end)

-- Split a stack. Data: {itemId, amount}
RegisterNUICallback('splitStack', function(data, cb)
    TriggerServerEvent('vantage_inventory:splitStack', data)
    cb('ok')
end)

-- Consume an item. Data: {slot}
RegisterNUICallback('consumeItem', function(data, cb)
    TriggerServerEvent('vantage_inventory:consumeItem', data.slot)
    cb('ok')
end)

-- Drop an item. Data: {itemId, amount}
RegisterNUICallback('dropItem', function(data, cb)
    TriggerServerEvent('vantage_inventory:dropItem', data)
    cb('ok')
end)

-- Give an item to a nearby player. Data: {itemId, amount}
RegisterNUICallback('giveItem', function(data, cb)
    TriggerServerEvent('vantage_inventory:giveItem', data)
    cb('ok')
end)