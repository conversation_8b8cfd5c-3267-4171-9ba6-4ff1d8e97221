-- SQL schema for the grid inventory system.

-- Create the main inventories table. Each inventory belongs to an
-- owner (player identifier or container id) and stores the grid
-- dimensions. The `owner` column is unique so players do not end up
-- with multiple inventories accidentally.
CREATE TABLE IF NOT EXISTS inventories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    owner VARCHAR(64) NOT NULL UNIQUE,
    type ENUM('player','container','drop','stash') NOT NULL DEFAULT 'player',
    width INT NOT NULL DEFAULT 10,
    height INT NOT NULL DEFAULT 6,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Items belonging to inventories. Each item has its own unique
-- `item_id` that is used by the client/server to track moves. The
-- `name` refers to an entry in shared/items.lua. Metadata is stored
-- as a JSON string for flexibility; values such as serial numbers and
-- durability are encoded inside. A composite key on (inventory_id,
-- item_id) ensures fast lookups.
CREATE TABLE IF NOT EXISTS inventory_items (
    inventory_id INT NOT NULL,
    item_id VARCHAR(32) NOT NULL,
    name VARCHAR(50) NOT NULL,
    slot_row INT NOT NULL,
    slot_col INT NOT NULL,
    width INT NOT NULL,
    height INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (inventory_id, item_id),
    FOREIGN KEY (inventory_id) REFERENCES inventories(id) ON DELETE CASCADE
);

-- Hotbar mapping table. Associates an item id with a hotbar slot for
-- each inventory. Slots are 1‑indexed. The `inventory_id` foreign
-- key allows cascaded deletion when an inventory is removed.
CREATE TABLE IF NOT EXISTS inventory_hotbar (
    id INT AUTO_INCREMENT PRIMARY KEY,
    inventory_id INT NOT NULL,
    slot INT NOT NULL,
    item_id VARCHAR(32) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inventory_id) REFERENCES inventories(id) ON DELETE CASCADE
);

-- Add indexes to improve performance for common queries. Composite
-- indexes on slot_row/slot_col accelerate overlap checks and unique
-- lookups.
CREATE INDEX IF NOT EXISTS idx_inventory_items_slot ON inventory_items (inventory_id, slot_row, slot_col);