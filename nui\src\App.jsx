import React, { useState, useEffect, useRef } from 'react'

/**
 * The top level component for the inventory NUI. It listens for
 * messages sent from the Lua client (via SendNUIMessage) and
 * updates local state accordingly. Users can drag and drop items
 * around the grid, rotate them by pressing R (handled via a
 * message), split stacks with shift, and interact with the
 * hotbar. When a drag ends the component sends a request back to
 * the client using fetch() which triggers a RegisterNUICallback on
 * the client script.
 */
export default function App() {
  const [isOpen, setOpen] = useState(false)
  const [inventory, setInventory] = useState([])
  const [hotbar, setHotbar] = useState({})
  const [grid, setGrid] = useState({ w: 10, h: 6 })
  const [dragging, setDragging] = useState(null)
  const gridRef = useRef(null)

  // Helper to send NUI callbacks back to the Lua client
  const sendNui = (event, data = {}) => {
    const resource = window.GetParentResourceName ? window.GetParentResourceName() : 'vantage_inventory'
    fetch(`https://${resource}/${event}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    })
  }

  useEffect(() => {
    function handleMessage(e) {
      const msg = e.data
      if (!msg || typeof msg !== 'object') return
      switch (msg.type) {
        case 'open':
          setInventory(msg.inventory || [])
          setHotbar(msg.hotbar || {})
          setGrid(msg.grid || grid)
          setOpen(true)
          break
        case 'close':
          setOpen(false)
          break
        case 'rotate':
          setDragging((d) => (d ? { ...d, rotate: !d.rotate } : d))
          break
        case 'quickdrop':
          if (dragging) {
            sendNui('dropItem', { itemId: dragging.item.id, amount: dragging.amount || dragging.item.quantity })
            setDragging(null)
          }
          break
        case 'quickgive':
          if (dragging) {
            sendNui('giveItem', { itemId: dragging.item.id, amount: dragging.amount || dragging.item.quantity })
            setDragging(null)
          }
          break
        default:
          break
      }
    }
    window.addEventListener('message', handleMessage)
    return () => window.removeEventListener('message', handleMessage)
  }, [dragging, grid])

  // Start dragging an item
  const onMouseDownItem = (item, e) => {
    if (!isOpen) return
    e.preventDefault()
    // Determine split amount when shift is held
    const splitAmount = e.shiftKey ? Math.floor(item.quantity / 2) : 0
    setDragging({
      item,
      offsetX: e.nativeEvent.offsetX,
      offsetY: e.nativeEvent.offsetY,
      rotate: false,
      amount: splitAmount,
      x: e.clientX,
      y: e.clientY,
    })
  }

  // Update ghost position when dragging
  const onMouseMove = (e) => {
    if (dragging) {
      e.preventDefault()
      setDragging((d) => ({ ...d, x: e.clientX, y: e.clientY }))
    }
  }

  // Finalise drag
  const onMouseUp = (e) => {
    if (dragging) {
      const { item, rotate, amount, offsetX, offsetY } = dragging
      const rect = gridRef.current.getBoundingClientRect()
      const cellWidth = rect.width / grid.w
      const cellHeight = rect.height / grid.h
      const x = e.clientX - rect.left - offsetX
      const y = e.clientY - rect.top - offsetY
      const col = Math.floor(x / cellWidth) + 1
      const row = Math.floor(y / cellHeight) + 1
      // Send move request to the server
      sendNui('moveItem', { itemId: item.id, toRow: row, toCol: col, splitAmount: amount || 0, rotate })
      setDragging(null)
    }
  }

  // Render nothing when closed
  if (!isOpen) return null

  // Precompute size of each cell (50px default). The container width
  // will scale with the number of columns to maintain a consistent
  // aspect ratio across different grid sizes.
  const cellSize = 50
  const gridStyle = {
    width: grid.w * cellSize,
    height: grid.h * cellSize,
    position: 'relative',
  }

  return (
    <div
      className="fixed inset-0 bg-black/40 flex items-center justify-center"
      onMouseMove={onMouseMove}
      onMouseUp={onMouseUp}
    >
      <div
        className="bg-slate-800 p-2 rounded shadow-lg relative"
        style={gridStyle}
        ref={gridRef}
      >
        {/* Grid background */}
        <div
          className="absolute inset-0 grid"
          style={{
            gridTemplateColumns: `repeat(${grid.w}, 1fr)`,
            gridTemplateRows: `repeat(${grid.h}, 1fr)`,
          }}
        >
          {Array.from({ length: grid.w * grid.h }).map((_, i) => (
            <div key={i} className="border border-gray-600 border-opacity-25"></div>
          ))}
        </div>
        {/* Items */}
        {inventory.map((item) => {
          const isDrag = dragging && dragging.item.id === item.id
          // Determine dimensions based on rotation
          const w = (isDrag && dragging.rotate ? item.height : item.width) * cellSize
          const h = (isDrag && dragging.rotate ? item.width : item.height) * cellSize
          const left = isDrag ? dragging.x - dragging.offsetX : (item.col - 1) * cellSize
          const top = isDrag ? dragging.y - dragging.offsetY : (item.row - 1) * cellSize
          const style = {
            width: w,
            height: h,
            left,
            top,
            position: 'absolute',
            zIndex: isDrag ? 50 : 10,
            opacity: isDrag ? 0.7 : 1,
            pointerEvents: 'auto',
          }
          return (
            <div
              key={item.id}
              onMouseDown={(e) => onMouseDownItem(item, e)}
              className="bg-gray-300 bg-opacity-20 border border-gray-500 text-white text-xs flex flex-col justify-between p-1 rounded select-none cursor-pointer"
              style={style}
            >
              <span className="truncate">{item.label}</span>
              <span className="text-right text-[10px]">x{item.quantity}</span>
            </div>
          )
        })}
      </div>
      {/* Hotbar */}
      <div className="absolute bottom-6 left-1/2 -translate-x-1/2 flex space-x-2 bg-slate-900/70 p-2 rounded">
        {Array.from({ length: 5 }).map((_, idx) => {
          const itemId = hotbar[idx + 1]
          const item = inventory.find((i) => i.id === itemId)
          return (
            <div
              key={idx}
              className="w-12 h-12 bg-slate-700 border border-gray-600 flex items-center justify-center relative"
            >
              {item && (
                <span className="text-white text-[10px] text-center px-1 leading-tight">
                  {item.label}
                  <br />x{item.quantity}
                </span>
              )}
              <span className="absolute bottom-0 right-0 text-[8px] text-gray-400 pr-0.5">{idx + 1}</span>
            </div>
          )
        })}
      </div>
    </div>
  )
}