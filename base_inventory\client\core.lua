InventoryOpen = false
KeyboardFocus = false

local function openInventory(screen)
    InventoryOpen = true
    SendNUIEvent("inventory:open", screen)
    SetNuiFocus(true, true)
    SetNuiFocusKeepInput(true)
    TriggerScreenblurFadeIn(0)
    QUI.TriggerEvent("toggleEffectMini", false)

    Controls.disable("lcontrol", true)

    while InventoryOpen do
        Wait(0)

        -- Disable player actions
        DisablePlayerControls({ general = true, attack = true, sprint = true, mouse = true })
        DisableControlAction(1, 69, true)  -- Shoot from vehicle
        DisableControlAction(1, 92, true)  -- Shoot from vehicle as passenger
        DisableControlAction(1, 142, true) -- Alternative melee control
        DisableControlAction(1, 200, true) -- ESC pause menu
        DisableControlAction(1, 257, true) -- yet another attack control

        if KeyboardFocus then
            DisableAllControlActions(0)
        end
    end
end

local function closeInventory()
    InventoryOpen = false
    TriggerServerEvent("inv:closed")
    SendNUIEvent("inventory:close")
    SetNuiFocus(false, false)
    QUI.TriggerEvent("toggleEffectMini", true)

    Controls.disable("lcontrol", false)

    TriggerScreenblurFadeOut(0)
end

local function toggleInventory()
    if InventoryOpen then
        closeInventory()
    else
        TriggerEvent("inventory:opened")
        openInventory()
    end
end

Controls.register("tab", "Inventory", toggleInventory)

CreateThread(function()
    while true do
        Wait(0)
        -- Disable the TAB key
        DisableControlAction(0, 37, true)
        DisableControlAction(0, 99, true)

        -- If ESC is pressed, close the inventory
        if InventoryOpen and IsDisabledControlJustReleased(0, 200) then
            closeInventory()
        end
    end
end)

RegisterNetEvent("inventory:open", function(screen)
    openInventory(screen)
end)

RegisterNetEvent("inventory:close", function()
    closeInventory()
end)

RegisterNetEvent("inventory:setScreen", function(screen)
    SendNUIEvent("inventory:setScreen", screen)
end)

RegisterNUICallback("inventory:focus", function(data, cb)
    KeyboardFocus = true
    Controls.disableAll(true)
    cb('OK')
end)

RegisterNUICallback("inventory:unfocus", function(data, cb)
    KeyboardFocus = false
    Controls.disableAll(false)
    cb('OK')
end)


-- Client side items API
local playerInventoryID = nil
local playerItems = {}
local itemChecks = {}
local handlers = {}

local function determineItems()
    local items = {}
    itemChecks = {}

    for _, item in pairs(playerItems) do
        itemChecks[item.item] = true
    end

    TriggerEvent("inventory:playerInventoryChange")
    TriggerServerEvent("inventory:playerInventoryChange")

    return items
end

local function doesPlayerHaveItem(item)
    if not playerInventoryID then
        print("Failed client item check: No playerInventoryID")
        return false
    end

    return itemChecks[item] or false
end
exports("doesPlayerHaveItem", doesPlayerHaveItem)

local function getPlayerItemData(itemName)
    itemName = string.lower(itemName)

    for _, item in pairs(playerItems) do
        if item.item == itemName then
            return item.data
        end
    end

    return nil
end
exports("getPlayerItemData", getPlayerItemData)

RegisterNetEvent("inventory:setPlayerInventory", function(inventoryID)
    playerItems = {}
    playerInventoryID = inventoryID

    for _, handler in ipairs(handlers) do
        RemoveEventHandler(handler)
    end

    table.insert(handlers, RegisterNetEvent("inventory:addItem:" .. playerInventoryID, function(item)
        if item then
            playerItems[item.id] = item
        end
        determineItems()
    end))

    table.insert(handlers, RegisterNetEvent("inventory:removeItem:" .. playerInventoryID, function(itemID)
        if itemID then
            playerItems[itemID] = nil
        end
        determineItems()
    end))

    table.insert(handlers, RegisterNetEvent("inventory:updateItem:" .. playerInventoryID, function(itemID, item)
        if itemID and item then
            playerItems[itemID] = table.assign(playerItems[itemID] or {}, item)
        end
        determineItems()
    end))

    table.insert(handlers, RegisterNetEvent("inventory:syncAllItems:" .. playerInventoryID, function(newItems)
        playerItems = {}
        for _, item in pairs(newItems) do
            playerItems[item.id] = item
        end
        determineItems()
    end))

    TriggerServerCallback("inventory:getPlayerItems", function(items)
        for _, item in pairs(items) do
            playerItems[item.id] = item
        end
        determineItems()
    end)
end)
