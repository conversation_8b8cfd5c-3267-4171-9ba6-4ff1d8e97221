--[[
    Default item definitions.

    This file registers several example items that demonstrate how
    consumables, weapons, magazines, armour and containers can be
    described. Each entry uses a simple table to define its
    characteristics. At runtime you can augment or replace this table
    via another resource or configuration; it is not hard‑coded. The
    server references this table when checking dimensions, weights and
    stackability.
]]

Items = {
    -- Weapons
    pistol = {
        label = '9mm Pistol',
        type = 'weapon',
        width = 2,
        height = 1,
        weight = 2.5,
        maxStack = 1,
        defaultMetadata = {
            serial = '', -- serial numbers are unique per instance
            durability = 100,
            attachments = {},
        },
    },
    assault_rifle = {
        label = 'Assault Rifle',
        type = 'weapon',
        width = 3,
        height = 1,
        weight = 4.5,
        maxStack = 1,
        defaultMetadata = {
            serial = '',
            durability = 100,
            attachments = {},
        },
    },
    smg = {
        label = 'SMG',
        type = 'weapon',
        width = 2,
        height = 1,
        weight = 3.5,
        maxStack = 1,
        defaultMetadata = {
            serial = '',
            durability = 100,
            attachments = {},
        },
    },

    -- Magazines and ammo. These stackable items hold ammunition for weapons.
    pistol_magazine = {
        label = 'Pistol Magazine',
        type = 'ammo',
        width = 1,
        height = 1,
        weight = 0.3,
        maxStack = 5,
        defaultMetadata = {
            ammoType = '9mm',
            count = 15,
        },
    },
    rifle_magazine = {
        label = 'Rifle Magazine',
        type = 'ammo',
        width = 1,
        height = 1,
        weight = 0.5,
        maxStack = 5,
        defaultMetadata = {
            ammoType = '5.56mm',
            count = 30,
        },
    },

    -- Consumables restore hunger, thirst or health.
    water_bottle = {
        label = 'Water Bottle',
        type = 'consumable',
        width = 1,
        height = 1,
        weight = 0.3,
        maxStack = 10,
        defaultMetadata = {
            thirstRestore = 25,
        },
    },
    burger = {
        label = 'Cheeseburger',
        type = 'consumable',
        width = 1,
        height = 1,
        weight = 0.4,
        maxStack = 10,
        defaultMetadata = {
            hungerRestore = 35,
        },
    },
    medkit = {
        label = 'Medkit',
        type = 'consumable',
        width = 2,
        height = 1,
        weight = 1.0,
        maxStack = 3,
        defaultMetadata = {
            healthRestore = 50,
        },
    },

    -- Armour and clothing persists on rejoin. These items occupy
    -- dedicated slots on the player and can degrade over time.
    body_armour = {
        label = 'Kevlar Vest',
        type = 'armour',
        width = 2,
        height = 2,
        weight = 3.0,
        maxStack = 1,
        defaultMetadata = {
            durability = 100,
        },
    },
    backpack = {
        label = 'Backpack',
        type = 'container',
        width = 2,
        height = 2,
        weight = 1.5,
        maxStack = 1,
        defaultMetadata = {
            capacity = {w = 6, h = 4},
        },
    },
}

return Items