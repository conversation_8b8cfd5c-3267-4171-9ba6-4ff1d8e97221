local currentWeapon = nil
local ammoAsCount = false
local reloading = false

local function isAmmoStoredAsCount(weaponHash)
    return GetWeapontypeGroup(weaponHash) == `GROUP_THROWN`
end

local function unequipAllWeapons()
    local playerPed = PlayerPedId()

    if currentWeapon then
        local weaponHash = GetHashKey(currentWeapon.item)
        local ammo = GetAmmoInPedWeapon(playerPed, weaponHash)

        TriggerServerEvent("inventory:updateAmmo", currentWeapon.id, ammo, isAmmoStoredAsCount(weaponHash))
    end

    RemoveAllPedWeapons(playerPed, true)
    -- GiveWeaponToPed(playerPed, "WEAPON_UNARMED", 1, false, false)

    currentWeapon = nil
    ammoAsCount = false
end

local function applyWeaponAttachments(weapon)
    local playerPed = PlayerPedId()

    local weaponHash = GetHashKey(weapon.item)

    local availableAttachments = WEAPON_ATTACHMENTS[weapon.item]
    if not availableAttachments then return end

    for itemName, componentHash in pairs(availableAttachments) do
        local hasAttachment = HasPedGotWeaponComponent(playerPed, weaponHash, componentHash)
        if weapon.data[itemName] and not hasAttachment then
            GiveWeaponComponentToPed(playerPed, weapon.item, componentHash)
        elseif not weapon.data[itemName] and hasAttachment then
            RemoveWeaponComponentFromPed(playerPed, weapon.item, componentHash)
        end
    end
end

local function givePedCurrentWeapon()
    local playerPed = PlayerPedId()

    if not currentWeapon then return end

    local weaponHash = GetHashKey(currentWeapon.item)
    local ammo = currentWeapon.data.ammo or 0
    if isAmmoStoredAsCount(weaponHash) then
        ammo = currentWeapon.count or 1
    end

    GiveWeaponToPed(playerPed, weaponHash, ammo, false, true)
    SetPedAmmo(playerPed, weaponHash, ammo)
    SetAmmoInClip(playerPed, weaponHash, ammo)

    -- Attachments
    applyWeaponAttachments(currentWeapon)
end

local function getWeaponAttachment(weapon, attachment)
	if (WEAPON_ATTACHMENTS[weapon] == nil) then return nil end
	return WEAPON_ATTACHMENTS[weapon][attachment]
end
exports("getWeaponAttachment", getWeaponAttachment)

RegisterNetEvent("inventory:setCurrentWeapon", function(weapon)
    local playerPed = PlayerPedId()
    while GetIsTaskActive(playerPed, 50) do -- Vaulting
        Wait(10)
    end

    if currentWeapon and currentWeapon.id == weapon.id then
        unequipAllWeapons()
        TriggerServerEvent("inventory:unequipAllWeapons")
        -- GiveWeaponToPed(playerPed, "WEAPON_UNARMED", 1, false, false)
        return
    end

    unequipAllWeapons()

    currentWeapon = weapon
    ammoAsCount = isAmmoStoredAsCount(GetHashKey(currentWeapon.item))

    givePedCurrentWeapon()
end)

-- Reloading
onBaseReady(function()
    Base.Controls.register("r", "Reload Weapon", function()
        if (not currentWeapon or ammoAsCount) or reloading then return end

        local ped = PlayerPedId()
        local maxAmmo = GetMaxAmmoInClip(ped, currentWeapon.item, true)
        local ammoInClip = GetAmmoInPedWeapon(ped, currentWeapon.item)

        if ammoInClip ~= maxAmmo then
            reloading = true
            TriggerServerCallback("inventory:attemptReload", function(success, newAmmo)
                if not success or not currentWeapon then
                    reloading = false
                    return
                end

                local weaponHash = GetHashKey(currentWeapon.item)
                newAmmo = newAmmo or maxAmmo

                if newAmmo > maxAmmo then
                    newAmmo = maxAmmo
                end

                SetAmmoInClip(ped, weaponHash, 0)
                SetPedAmmo(ped, weaponHash, newAmmo)
                local _, selectedWeaponHash = GetCurrentPedWeapon(ped, true)
                if IsPedInAnyVehicle(ped, true) and weaponHash == selectedWeaponHash then
                    TaskReloadWeapon(ped, false) -- suck shit car warefare meta hahahahahahahhahahahaha - brad
                else
                    MakePedReload(ped)
                end

                Base.Notification("Reloading...", { timeout = 2000 })

                TriggerServerEvent("inventory:updateAmmo", currentWeapon.id, newAmmo, isAmmoStoredAsCount(weaponHash))
                currentWeapon.data.ammo = newAmmo
                reloading = false

                -- Patch for vehicles where the weapon had no ammo
                Wait(2000)
                _, selectedWeaponHash = GetCurrentPedWeapon(ped)
                if GetHashKey(currentWeapon.item) ~= selectedWeaponHash then
                    -- unequipAllWeapons()
                    givePedCurrentWeapon()
                end
            end, currentWeapon.id, maxAmmo, ammoInClip)
        end -- Don't reload if already max ammo
    end)
end)

CreateThread(function()
    while true do
        Wait(100)
        local ped = PlayerPedId()
        local wep = GetSelectedPedWeapon(ped)

        if currentWeapon then
            local bool, ammo = GetAmmoInClip(ped, wep)
            local ammoWeapon = GetAmmoInPedWeapon(ped, wep)

            SetWeaponsNoAutoswap(true)

            if IsPedInAnyVehicle(ped, false) and ammoWeapon == 0 and not IsPedGettingIntoAVehicle(ped) then
                if ammo <= 0 then
                    TaskSwapWeapon(ped, true)
                    Wait(0)
                    ClearPedTasks(ped, 1)
                end
            end
        end
    end
end)

CreateThread(function()
    while true do
        Wait(0)

        -- Reload
        DisableControlAction(0, 45)
        DisableControlAction(0, 80)
        DisableControlAction(0, 140)

        -- Sets Settings to Freeaim (No Controller Aim Assist)
        SetPlayerTargetingMode(3)
    end
end)
