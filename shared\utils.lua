--[[
    Shared utility functions used by both client and server.

    The helpers in this file are kept framework agnostic. They handle
    mundane tasks like generating unique IDs, encoding/decoding metadata
    tables to JSON, and simple data validation. When adding new helpers
    make sure they do not require access to FiveM specific functions so
    that they remain usable on both client and server.
]]

local Utils = {}

-- Generate a unique identifier for an item instance. Lua’s math.random
-- combined with os.time gives a reasonable pseudo‑unique string for
-- demonstration purposes. For production use you may want to switch to
-- something like UUID4.  As every call yields a new value there’s a
-- minimal chance of collision.
function Utils.GenerateId()
    local template ='xxxxxxxx'
    return template:gsub('x', function()
        return string.format('%x', math.random(0, 15))
    end) .. os.time()
end

-- Encode a Lua table as a JSON string. This wrapper handles nil values
-- gracefully by returning an empty object. Use this whenever writing
-- metadata into the database.
function Utils.EncodeMetadata(tbl)
    if not tbl or type(tbl) ~= 'table' then
        return '{}'
    end
    local ok, res = pcall(json.encode, tbl)
    return ok and res or '{}'
end

-- Decode a JSON string back into a Lua table. Returns an empty table if
-- decoding fails. Use this whenever reading metadata from the database.
function Utils.DecodeMetadata(str)
    if not str or str == '' then
        return {}
    end
    local ok, res = pcall(json.decode, str)
    return ok and res or {}
end

-- Deep copy a table. Inventories maintain nested tables for items and
-- metadata. Using a deep copy ensures changes to one instance don’t
-- inadvertently mutate another.
function Utils.DeepCopy(orig)
    local orig_type = type(orig)
    local copy
    if orig_type == 'table' then
        copy = {}
        for k, v in next, orig, nil do
            copy[Utils.DeepCopy(k)] = Utils.DeepCopy(v)
        end
        setmetatable(copy, Utils.DeepCopy(getmetatable(orig)))
    else
        copy = orig
    end
    return copy
end

return Utils