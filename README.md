# Grid‑based Inventory System

This resource implements a fully fledged inventory for FiveM inspired by
survival games like **DayZ** and **Escape from Tarkov**.  It replaces the
default GTA V weapon wheel entirely and provides a responsive grid
interface that supports drag–&–drop, stacking, rotation, splitting and
hotbar quick‑use.  The system is framework‑agnostic: it detects and
integrates with **ESX**, **QBCore** and **Qbox** automatically but can
also run in a standalone mode.

## Why build your own inventory?

Existing solutions such as `ox_inventory` demonstrate many best
practices.  For example, they note that all interactions are
server‑validated and synchronised so multiple players can access the same
inventory without duplication【975483972931357†L318-L327】.  Items are stored per slot with
customisable metadata (serial numbers, durability, attachments)【975483972931357†L331-L336】 and
containers provide access to trunks, gloveboxes and backpacks【975483972931357†L346-L352】.  Our
implementation follows a similar philosophy but is written from the
ground up for educational purposes and to demonstrate how such a system
can integrate with multiple frameworks.  Database operations are
performed through **OxMySQL**, a high‑performance MySQL wrapper that
supports asynchronous queries, prepared statements and transaction
support【530083255573621†L20-L35】, ensuring your server remains responsive even under
load【530083255573621†L39-L50】.  Transaction support allows us to wrap multi‑step
inventory moves into a single atomic operation【530083255573621†L69-L73】.

The vanilla weapon wheel is disabled entirely by setting ped config
flag 48 to `true`【983711235337996†L97-L106】.  This prevents players from switching
weapons outside of the inventory or hotbar.

## Features

* **Grid‑based UI** – Items have width and height and cannot overlap.  Drag
  items around, rotate them with **R** and split stacks by holding
  **Shift** while dragging.  The UI is implemented in React + Tailwind and
  runs at 60 FPS thanks to Vite.
* **Hotbar slots 1–5** – Assign items to quick‑use slots and press the
  corresponding number key to equip or consume them on the fly.
* **Containers & stashes** – Create inventories for trunks, gloveboxes,
  bags or house stashes.  Containers are treated identically to player
  inventories on the backend, making it trivial to add new storage
  types【975483972931357†L346-L352】.
* **Metadata** – Each item instance stores arbitrary JSON metadata such
  as serial numbers, durability, skins or attachments【975483972931357†L331-L336】.  Weapons
  become items with attachments and ammo magazines, armour persists
  across sessions and consumables restore health, hunger or thirst.
* **Server authority** – All actions are validated server‑side.  Only the
  server updates slot positions, stack counts and metadata.  If the
  client attempts an invalid move or exceeds the configured
  rate‑limit the UI will be resynchronised and the offending action
  discarded.  Important events (drops, splits, trades) can be logged
  when `Config.DebugLogging` is enabled.
* **Rate limiting** – Each player is allowed a configurable number of
  inventory operations per second.  Exceeding the limit will force a
  resync.  This helps mitigate duplication exploits.
* **Framework integration** – Detects and adapts to ESX, QBCore and
  Qbox automatically.  Exports (`AddItem`, `RemoveItem`, `GetInventory`,
  `OpenContainer` and `GiveLoadout`) allow other resources to give
  players items or open containers without worrying about the underlying
  framework.
* **Persistence** – Inventory state is stored in MySQL via OxMySQL.  The
  schema is provided in `sql/migrations.sql`.  Each player/container
  inventory is persisted independently and loaded on demand.

## Installation

1. **Install OxMySQL (optional).**  Download the `oxmysql` resource, drop it into
   Only required when `Config.UseDatabase = true`; skip this step for in‑memory persistence.
   your `resources` folder and add `ensure oxmysql` to your
   `server.cfg`.  OxMySQL runs queries asynchronously and sanitises
   inputs using prepared statements【530083255573621†L20-L50】.
2. **Import the schema (optional).**  Execute the SQL found in
   Only required when `Config.UseDatabase = true`; skip this step when running without a database.
   `vantage_inventory/sql/migrations.sql` on your MySQL server.  This
   creates the tables used by the inventory.
3. **Add the resource.**  Copy the `vantage_inventory` folder into your
   `resources` directory and add `ensure vantage_inventory` below your
   framework (`es_extended`, `qb-core` or `qbx_core`).
4. **Configure.**  Edit `vantage_inventory/config.lua` to adjust grid
   dimensions, weight limits, keybindings or framework override.  By
   default the system auto‑detects the running framework.
5. **Build the UI.**  Navigate into the `vantage_inventory/nui` folder,
   run `npm install` and then `npm run build`.  This compiles the React
   app with Vite and outputs the assets into `nui/dist`, which FiveM
   serves as the NUI.  During development you can run `npm run dev` and
   change `ui_page` in `fxmanifest.lua` to `"http://localhost:5173"`.

## Keybinds

The default key mappings can be changed via `Config.Keybinds`.  Players
can override them in the FiveM settings menu.  The following keys are
bound out of the box:

| Action            | Key | Description                         |
|-------------------|----:|--------------------------------------|
| Toggle inventory  |  TAB| Open/close the grid UI               |
| Hotbar slot 1–5   | 1–5 | Equip/use the item in that slot      |
| Rotate            |   R | Rotate the item during a drag        |
| Split stack       | Shift| Hold while dragging to split stack  |
| Adjust amount     |  Alt| (Reserved for future use)            |
| Quick drop        |   F | Hold to drop the dragged item        |
| Quick give        |   G | Hold to give to nearest player       |

## Development Notes

* The UI uses simple HTML5 drag events coupled with React state.
  When a drag ends the NUI calls back into the Lua client via
  `fetch("https://vantage_inventory/moveItem")`.  These callbacks
  correspond to the `RegisterNUICallback` handlers in
  `client/main.lua`.
* The default ped weapon wheel is disabled by repeatedly applying
  `SetPedConfigFlag(ped, 48, true)` on the client【983711235337996†L97-L106】.
  This must be re‑applied after respawn because ped entities change.
* Only the server ever modifies the authoritative state.  Moves
  are validated for overlap and bounds on the server; if a client
  attempt is invalid the change is ignored.
* To extend the item list create new entries in `shared/items.lua`.  The
  `defaultMetadata` table is merged into each new instance.  If you
  register new weapons in your framework remember to also call
  `exports['vantage_inventory']:AddItem()` accordingly.

## Acknowledgements

This resource was inspired by the design philosophies of
Overextended’s `ox_inventory` project and OxMySQL.  The server side
code uses transactions and prepared statements to prevent duplication
and SQL injection【530083255573621†L45-L49】.  The UI design takes cues from
modern survival games and emphasises clarity and responsiveness.