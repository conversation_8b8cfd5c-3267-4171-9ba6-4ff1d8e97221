local db = RegisterChunkDataHandler("droppedItems")

local proxItems = {}

-- Tell the server we're ready!
CreateThread(function()
    while not NetworkIsPlayerActive(PlayerId()) or not PlayerPedId() or not GetEntityCoords(PlayerPedId()) do
        Wait(0)
    end
    Wait(1000)
    TriggerServerEvent("droppedItems:subscribe")
end)

db.onSet = function(k, v, c)
    if proxItems[k] then
        if proxItems[k].object then
            DeleteEntity(proxItems[k].object)
        end

        if proxItems[k].inProx then
            SendNUIMessage({
                removeProximityItem = k
            })
        end
    end

    local model = v.model or "hei_prop_hei_paper_bag"
    local obj = CreateObject(GetHashKey(model), v.coords.x, v.coords.y, v.coords.z - 0.95, false, false, false)
    FreezeEntityPosition(obj, true)
    SetEntityCollision(obj, false, false)
    PlaceObjectOnGroundProperly(obj)

    if v.rotation then
        SetEntityHeading(obj, v.rotation)
    end

    v.object = obj

    proxItems[k] = v
end

db.onRemove = function(k)
    local item = proxItems[k]
    if not item then return end

    if item and item.object then
        DeleteEntity(item.object)
    end

    if item.inProx then
        SendNUIMessage({
            removeProximityItem = k
        })
    end

    proxItems[k] = nil
end

db.onClear = function()
    for id, item in pairs(proxItems) do
        if item.object then
            DeleteEntity(item.object)
        end

        if item.inProx then
            SendNUIMessage({
                removeProximityItem = id
            })
        end
    end

    proxItems = {}
end

CreateThread(function()
    while true do
        Wait(0)

        local items = table.clone(proxItems)

        for k, item in pairs(items) do
            Wait(0)

            local coords = GetEntityCoords(PlayerPedId())

            if proxItems[k] and item.object then
                local dist = #(coords - item.coords)
                if item.inProx and dist > 3.0 then
                    proxItems[k].inProx = false
                    SendNUIMessage({
                        removeProximityItem = k
                    })
                elseif not item.inProx and dist < 3.0 then
                    proxItems[k].inProx = true
                    SendNUIMessage({
                        addProximityItem = item
                    })
                end
            end
        end
    end
end)
