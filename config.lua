--[[
    Configuration for the grid‑based inventory system.

    This file exposes a single global table `Config` which is loaded on
    both the client and the server. Adjust these values to suit your
    server’s needs – grid size, weight limits, keybindings and despawn
    timers can all be tuned without modifying the core scripts.

    Keybindings specify defaults but can be overridden by players through
    the FiveM key mapping system. Because this resource avoids text
    commands (e.g. `/inv`), every action is tied to a key.
]]

Config = {}

-- Framework selection. When set to 'auto' the server will probe for ESX,
-- QBCore or Qbox at runtime and pick the first one it detects. You can
-- force a specific framework by setting this to 'ESX', 'QBCore' or 'Qbox'.
Config.Framework = 'auto'

-- Default size of a player’s inventory grid. The inventory is measured in
-- columns (width) and rows (height). Each item has a width and height in
-- grid units; items cannot overlap and must fit within the boundaries of
-- the inventory. Increase these values to give players more space.
Config.GridWidth = 10
Config.GridHeight = 6

-- Use weight based limits instead of slot based limits. When true, each
-- item consumes a configurable amount of weight and players cannot carry
-- more than `MaxWeight`. When false, the inventory simply enforces the
-- grid bounds. You can leave both enabled by giving each item a weight
-- and still respecting the grid.
Config.UseWeight = false
Config.MaxWeight = 100.0

-- Keybinds define the default keys used on keyboard and gamepad. Players
-- can override these using the key mapping options. Note that some keys
-- are reserved by the game; choose wisely to avoid conflicts.
Config.Keybinds = {
    ToggleInventory = 'TAB',      -- open/close the inventory
    Hotbar = {'1','2','3','4','5'}, -- quick use hotbar slots
    Rotate = 'R',                 -- rotate item during drag
    SplitStackModifier = 'SHIFT', -- modifier for splitting stacks
    AdjustStackModifier = 'LALT', -- modify stack amount with mouse wheel
    QuickDrop = 'F',             -- hold to drop item on ground
    QuickGive = 'G'              -- hold to give item to nearest player
}

-- Despawn time for dropped items (seconds). Items dropped on the ground
-- will be removed after this period to avoid clutter and performance
-- issues. Set to zero to disable despawning entirely.
Config.ContainerDespawn = 300 -- 5 minutes

-- Anti‑dupe settings. Rate limits inventory operations to prevent abuse.
-- You can adjust the number of allowed operations per second globally or
-- per player. If a player exceeds the limit their inventory will be
-- resynchronised to the server’s authoritative state.
Config.OperationRateLimit = 20 -- actions per second per player

-- Whether to log inventory actions on the server console. Useful for
-- debugging and auditing. Set to false in production if you do not want
-- verbose logs.
Config.DebugLogging = true

-- UseDatabase controls whether the inventory persists to a MySQL
-- database via oxmysql. When false all inventory state is kept in
-- memory and will reset on server restart. Set this to false if
-- running without any database.
Config.UseDatabase = false