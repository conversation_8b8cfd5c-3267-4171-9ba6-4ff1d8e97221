--[[
    Grid-based Inventory System for FiveM
    
    This fxmanifest defines the metadata for the resource, specifies the
    entrypoints for the client and server, and declares the NUI files. The
    `lua54` directive enables modern Lua features. When building the UI you
    can run `npm install` followed by `npm run build` inside the `nui` folder
    to generate the production ready assets under `nui/dist`.

    Author: ChatGPT Agent
]]

fx_version 'cerulean'
game 'gta5'

author 'Vantage'
description 'Vantage – a standalone grid-based drag-and-drop inventory system (no database required)'
version '1.0.0'

-- Use Lua 5.4 (required for some language features).
lua54 'yes'

-- Define the webpage for the NUI. This HTML file loads the React bundle and
-- mounts the inventory UI. During development you can point this to
-- `http://localhost:5173` when running `npm run dev` inside the `nui` folder.
ui_page 'nui/dist/index.html'

-- Files required by the NUI. When you build the React app the output will
-- reside under `nui/dist`. The wildcard allows any assets (JS, CSS, images)
-- produced by Vite to be loaded by FiveM.
files {
    'nui/dist/index.html',
    'nui/dist/assets/*'
}

client_scripts {
    'config.lua',
    'client/main.lua'
}

server_scripts {
    'config.lua',
    'server/main.lua'
}

-- Shared scripts are executed on both client and server. Place definitions
-- that need to be available everywhere here (e.g. shared enums or helpers).
shared_scripts {
    'shared/items.lua',
    'shared/utils.lua'
}

-- Exports allow other resources to call into this inventory system in a
-- framework‑agnostic way. See `server/main.lua` for implementation.
exports {
    'AddItem',
    'RemoveItem',
    'GetInventory',
    'OpenContainer',
    'GiveLoadout'
}