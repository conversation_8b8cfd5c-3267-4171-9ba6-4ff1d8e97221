import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// Vite configuration for the NUI. The base is set to './' so that
// assets are referenced relative to the index.html location when
// loaded by the FiveM NUI. Output is written to the `dist` folder.
export default defineConfig({
  base: './',
  plugins: [react()],
  build: {
    outDir: 'dist',
    emptyOutDir: true
  },
  server: {
    port: 5173,
    strictPort: true
  }
})