-- Animations
local animations = {}

clothingOptions = {
    { label = "Shirt",   anim = "shirt",   index = 6 },
    { label = "Pants",   anim = "pants",   index = 7 },
    { label = "Shoes",   anim = "shoes",   index = 8 },
    { label = "Hat",     anim = "hat",     index = 1 },
    { label = "Gloves",  anim = "gloves",  index = 9 },
    { label = "Bag",     anim = "bag",     index = 10 },
    { label = "Vest",    anim = "vest",    index = 5 },
    { label = "Mask",    anim = "mask",    index = 2 },
    { label = "Glasses", anim = "glasses", index = 3 },
    { label = "Chain",   anim = "chain",   index = 4 },
}

RegisterDataSyncHandler("animations", function(animData)
    animations = {}

    for _, v in pairs(animData) do
        if animations[v.category] == nil then
            animations[v.category] = {}
        end
        animations[v.category][v.label] = v
    end

    for _, v in pairs(clothingOptions) do
        if animations["Clothing"] == nil then
            animations["Clothing"] = {}
        end
        animations["Clothing"][v.label] = v
    end
end, "main_roleplay")

RegisterNUICallback("animations:request", function(data, cb)
    cb(animations)
end)
