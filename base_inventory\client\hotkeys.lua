-- Hotkeys
local activeHotbar = 1
local hotkeys = {}
local hotkeysBlocked = false

local function disableHotkeys(state)
    hotkeysBlocked = state
end
exports("disableHotkeys", disableHotkeys)

local function hotbarID()
    return "inventory:hotkeys:" .. LocalPlayer.state.charid .. ":" .. activeHotbar
end

local function refreshHotkeys()
    if not LocalPlayer.state.charid then
        return
    end

    local storedValue = GetResourceKvpString(hotbarID())
    local data = json.decode(storedValue or "{}")

    hotkeys = {}
    for i = 1, 9 do
        hotkeys[i] = data[i] or {}
    end

    SendNUIEvent("hotkeys:load", { hotkeys = hotkeys, activeHotbar = activeHotbar })
    SendNUIEvent("hotkeys:switched")
end

AddPrivateStateHandler("currentWeapon", function(key, value)
    SendNUIEvent("hotkeys:equippedWeapon", key)
end)

CreateThread(function()
    Wait(1000)

    if not LocalPlayer.state.charid then
        return
    end

    refreshHotkeys()
end)

RegisterNetEvent("base:characterLoaded", function()
    refreshHotkeys()
end)

RegisterNUICallback("hotkeys:setSlot", function(data, cb)
    local slot = tonumber(data.slot)
    if not slot then
        return cb("OK")
    end

    if not data.label then data.label = data.value end

    if data.type and data.value then
        hotkeys[slot] = data
    else
        hotkeys[slot] = {}
    end

    SetResourceKvp(hotbarID(), json.encode(hotkeys))
    refreshHotkeys()
    cb("OK")
end)

RegisterNUICallback("hotkeys:clearSlot", function(data, cb)
    local slot = tonumber(data.slot) + 1
    hotkeys[slot] = {}
    SetResourceKvp(hotbarID(), json.encode(hotkeys))
    refreshHotkeys()

    cb("OK")
end)

RegisterNUICallback("hotkeys:setActiveHotbar", function(data, cb)
    local hotbar = tonumber(data.hotbar)
    if not hotbar or hotbar < 1 or hotbar > 5 then
        return cb("OK")
    end

    activeHotbar = hotbar
    refreshHotkeys()
    cb("OK")
end)

RegisterNUICallback("hotkeys:disableHotkeys", function(data, cb)
    local state = data.state
    disableHotkeys(state)
    cb("OK")
end)

-- Control Thread
local controls = {
    ["1"] = 157,
    ["2"] = 158,
    ["3"] = 160,
    ["4"] = 164,
    ["5"] = 165,
    ["6"] = 159,
    ["7"] = 161,
    ["8"] = 162,
    ["9"] = 163,
}

local function useHotkey(slot)
    local hotkey = hotkeys[slot]
    SendNUIEvent("hotkeys:select", slot, hotkey.value)

    if hotkey then
        if hotkey.type == "item" then
            TriggerServerEvent("inventory:useItemByName", hotkey.value, "Use Item")
        elseif hotkey.type == "animation" then
            if LocalPlayer.state.inJail then
                return
            end
            exports.main_roleplay:startDatabaseAnim(hotkey.value)
        elseif hotkey.type == "recipe" then
            TriggerServerEvent("inventory:craftItem", hotkey.value, { quantity = 1 })
        end
    end
end

CreateThread(function()
    while true do
        Wait(0)

        if not KeyboardFocus and not hotkeysBlocked then
            for key, control in pairs(controls) do
                DisableControlAction(0, control, true)
                if IsDisabledControlJustPressed(0, control) then
                    local slot = tonumber(key)

                    -- If left control is pressed, switch hotbar
                    if slot and slot >= 1 and slot <= 5 and IsDisabledControlPressed(0, 36) then
                        activeHotbar = slot
                        refreshHotkeys()
                    else
                        -- Otherwise, use the hotkey
                        useHotkey(slot)
                    end
                end
            end
        end
    end
end)
