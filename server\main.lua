--[[
    Server side inventory controller.

    The server maintains authoritative inventories for players and
    containers, validates all operations, and communicates with the
    database using oxmysql. Client interactions are considered
    untrusted; every move, split or consume request is checked against
    the server state before being applied. When a mismatch occurs the
    player’s UI is resynchronised and a rate limit penalty is applied.

    OxMySQL supports asynchronous queries and transaction support
    enabling multiple related queries to be executed atomically【530083255573621†L69-L73】. We
    leverage this feature when moving items to ensure that updates to
    slot positions, stack counts and metadata are committed together.

    The resource automatically detects whether ESX, QBCore or Qbox is
    loaded. You can override detection in the config by setting
    Config.Framework to 'ESX', 'QBCore' or 'Qbox'.
]]

local Config = Config
local Items = require('shared/items')
local Utils = require('shared/utils')

-- Framework detection
local Framework = nil
local FW = nil

CreateThread(function()
    -- Delay until all resources have started
    Wait(500)
    local frameworkOverride = Config.Framework:lower()
    if frameworkOverride ~= 'auto' then
        Framework = frameworkOverride
    else
        -- Attempt to detect ESX
        if GetResourceState('es_extended') == 'started' then
            Framework = 'esx'
            FW = exports['es_extended']:getSharedObject()
        elseif GetResourceState('qb-core') == 'started' then
            Framework = 'qbcore'
            FW = exports['qb-core']:GetCoreObject()
        elseif GetResourceState('qbx_core') == 'started' or GetResourceState('qbox_core') == 'started' or GetResourceState('qbox') == 'started' then
            Framework = 'qbox'
            -- Qbox exposes its core object similarly to QBCore; fallback
            if exports['qbx_core'] then
                FW = exports['qbx_core']:GetCoreObject()
            elseif exports['qbox_core'] then
                FW = exports['qbox_core']:GetCoreObject()
            elseif exports['qbox'] then
                FW = exports['qbox']:GetCoreObject()
            end
        else
            Framework = nil
            print('[vantage_inventory] No supported framework detected – running in standalone mode.')
        end
    end
    if Config.DebugLogging then
        print(('[vantage_inventory] Using framework: %s'):format(Framework or 'none'))
    end
end)

-- In‑memory cache of inventories keyed by player source or container id.
-- Each entry stores width, height, type and an items table. Items are
-- keyed by their unique id so lookups are O(1). We persist changes
-- periodically and whenever the player disconnects.
local Inventories = {}

-- Rate limiting state. Each player has a counter of operations
-- performed within the current second. When the number of operations
-- exceeds Config.OperationRateLimit the player is forcefully resynced.
local RateLimit = {}

-- Whether to use MySQL for persistence. When false all inventories are
-- kept in memory and will not survive server restarts.
local USE_DATABASE = Config.UseDatabase ~= false

-- Utility: get identifier for a player. ESX uses identifier from
-- `getIdentifier`, QBCore uses `Player.PlayerData.citizenid` and Qbox
-- uses `Player.PlayerData.source` by default. Fallback to source.
local function getIdentifier(source)
    if Framework == 'esx' then
        local xPlayer = FW.GetPlayerFromId(source)
        return xPlayer and xPlayer.identifier or ('player:%s'):format(source)
    elseif Framework == 'qbcore' or Framework == 'qbox' then
        local Player = FW.Functions.GetPlayer(source)
        return Player and Player.PlayerData and (Player.PlayerData.citizenid or Player.PlayerData.identifier) or ('player:%s'):format(source)
    else
        return ('player:%s'):format(source)
    end
end

-- Create a new blank inventory for a given owner. Persist to DB and
-- return the internal inventory structure. This is called when a new
-- player joins for the first time.
local function createInventory(owner, invType)
    local width = Config.GridWidth
    local height = Config.GridHeight
    local inv = {
        owner = owner,
        type = invType or 'player',
        width = width,
        height = height,
        items = {},
        hotbar = {},
    }
    if USE_DATABASE then
        -- Insert into the `inventories` table. Use prepared statements to
        -- prevent SQL injection. OxMySQL sanitizes queries via prepared
        -- statements【530083255573621†L45-L49】.
        local insertId = MySQL.insert.await('INSERT INTO inventories (owner, type, width, height) VALUES (?,?,?,?)', {
            owner, inv.type, width, height
        })
        inv.id = insertId
    else
        -- When not using a database we derive an id from the owner. This
        -- id is only used internally and does not need to be numeric.
        inv.id = owner
    end
    Inventories[owner] = inv
    return inv
end

-- Load an inventory from the database into memory. If the inventory
-- doesn’t exist it will be created on demand.
local function loadInventory(owner, invType)
    if Inventories[owner] then return Inventories[owner] end
    if USE_DATABASE then
        local row = MySQL.single.await('SELECT * FROM inventories WHERE owner = ? LIMIT 1', {owner})
        local inv
        if row then
            inv = {
                id = row.id,
                owner = row.owner,
                type = row.type,
                width = row.width,
                height = row.height,
                items = {},
                hotbar = {}
            }
            -- Load items
            local items = MySQL.query.await('SELECT * FROM inventory_items WHERE inventory_id = ?', {row.id})
            for _, itemRow in ipairs(items) do
                local itemId = itemRow.item_id
                inv.items[itemId] = {
                    id = itemRow.item_id,
                    name = itemRow.name,
                    row = itemRow.slot_row,
                    col = itemRow.slot_col,
                    width = itemRow.width,
                    height = itemRow.height,
                    quantity = itemRow.quantity,
                    metadata = Utils.DecodeMetadata(itemRow.metadata)
                }
            end
            -- Load hotbar (optional). Example uses a table keyed by slot index.
            local hotbar = MySQL.query.await('SELECT * FROM inventory_hotbar WHERE inventory_id = ? ORDER BY slot ASC', {row.id})
            for _, hRow in ipairs(hotbar) do
                inv.hotbar[hRow.slot] = hRow.item_id
            end
        else
            inv = createInventory(owner, invType)
        end
        Inventories[owner] = inv
        return inv
    else
        -- Without a database simply create a new inventory if one doesn’t exist
        return createInventory(owner, invType)
    end
end

-- Save a single item to the database. Upsert semantics: insert if
-- item_id does not exist otherwise update existing row. When deleting
-- items call deleteItem().
local function saveItem(invId, item)
    if USE_DATABASE then
        MySQL.prepare.await([[INSERT INTO inventory_items (inventory_id, item_id, name, slot_row, slot_col, width, height, quantity, metadata)
            VALUES (?,?,?,?,?,?,?,?,?)
            ON DUPLICATE KEY UPDATE slot_row = VALUES(slot_row), slot_col = VALUES(slot_col), width = VALUES(width), height = VALUES(height), quantity = VALUES(quantity), metadata = VALUES(metadata)]], {
            invId,
            item.id,
            item.name,
            item.row,
            item.col,
            item.width,
            item.height,
            item.quantity,
            Utils.EncodeMetadata(item.metadata)
        })
    end
end

-- Remove an item record from the database.
local function deleteItem(invId, itemId)
    if USE_DATABASE then
        MySQL.prepare.await('DELETE FROM inventory_items WHERE inventory_id = ? AND item_id = ?', {invId, itemId})
    end
end

-- Save the hotbar mapping to the database. Each call rewrites the
-- existing mapping for the inventory. This naive approach simplifies
-- the code; you could optimise by only updating changed slots.
local function saveHotbar(inv)
    if not inv.id then return end
    if USE_DATABASE then
        -- Remove existing rows
        MySQL.prepare.await('DELETE FROM inventory_hotbar WHERE inventory_id = ?', {inv.id})
        -- Insert new rows
        for slot, itemId in pairs(inv.hotbar) do
            MySQL.insert.await('INSERT INTO inventory_hotbar (inventory_id, slot, item_id) VALUES (?,?,?)', {inv.id, slot, itemId})
        end
    end
end

-- Convert the internal inventory structure to a serialisable object
-- for sending to the client. We don’t send width/height because the
-- client reads grid size from Config. Each item includes its id,
-- definition (from Items) and metadata so the UI can render tooltips.
local function serialiseInventory(inv)
    local list = {}
    for _, item in pairs(inv.items) do
        local def = Items[item.name]
        if def then
            list[#list+1] = {
                id = item.id,
                name = item.name,
                label = def.label,
                row = item.row,
                col = item.col,
                width = item.width,
                height = item.height,
                quantity = item.quantity,
                weight = def.weight,
                metadata = item.metadata
            }
        end
    end
    return list
end

-- Rate limit check. Returns true if the player may proceed. Otherwise
-- triggers a resync and returns false.
local function checkRateLimit(source)
    local now = GetGameTimer()
    local entry = RateLimit[source]
    if not entry or now - entry.lastReset > 1000 then
        RateLimit[source] = { count = 1, lastReset = now }
        return true
    end
    entry.count = entry.count + 1
    if entry.count > Config.OperationRateLimit then
        -- Too many operations; resynchronise the client
        TriggerClientEvent('vantage_inventory:forceClose', source)
        -- send fresh inventory after a short delay
        SetTimeout(250, function()
            local inv = loadInventory(getIdentifier(source))
            TriggerClientEvent('vantage_inventory:update', source, serialiseInventory(inv), inv.hotbar)
        end)
        return false
    end
    return true
end

-- Validate whether an item placement is legal. Ensures item stays within
-- grid bounds and does not overlap any other items. Returns true/false.
local function isPlacementValid(inv, item, row, col, width, height, ignoreItemId)
    if row < 1 or col < 1 or (col + width - 1) > inv.width or (row + height - 1) > inv.height then
        return false
    end
    for _, other in pairs(inv.items) do
        if other.id ~= ignoreItemId then
            local oMinRow = other.row
            local oMaxRow = other.row + other.height - 1
            local oMinCol = other.col
            local oMaxCol = other.col + other.width - 1
            local nMinRow = row
            local nMaxRow = row + height - 1
            local nMinCol = col
            local nMaxCol = col + width - 1
            -- Check overlap
            if not (nMaxRow < oMinRow or nMinRow > oMaxRow or nMaxCol < oMinCol or nMinCol > oMaxCol) then
                return false
            end
        end
    end
    return true
end

-- Server exports ------------------------------------------------------------

-- Add an item to a player’s inventory. Searches for a free slot that fits
-- the item; if none exists the function returns false. Optional
-- metadata overrides default values. When `quantity` exceeds the item’s
-- max stack size the function will attempt to create multiple stacks.
function AddItem(playerSource, itemName, quantity, metadata)
    local src = playerSource
    local inv = loadInventory(getIdentifier(src))
    local def = Items[itemName]
    if not def then return false end
    quantity = quantity or 1
    metadata = metadata or {}
    -- Attempt to stack into existing items first
    for _, item in pairs(inv.items) do
        if item.name == itemName and def.maxStack > 1 and item.quantity < def.maxStack then
            local canAdd = math.min(quantity, def.maxStack - item.quantity)
            item.quantity = item.quantity + canAdd
            saveItem(inv.id, item)
            quantity = quantity - canAdd
            if quantity <= 0 then break end
        end
    end
    -- Place remaining quantity into new item instances
    while quantity > 0 do
        local qty = math.min(quantity, def.maxStack)
        -- find a free position
        local placed = false
        for r = 1, inv.height do
            for c = 1, inv.width do
                if isPlacementValid(inv, nil, r, c, def.width, def.height, nil) then
                    local id = Utils.GenerateId()
                    local newItem = {
                        id = id,
                        name = itemName,
                        row = r,
                        col = c,
                        width = def.width,
                        height = def.height,
                        quantity = qty,
                        metadata = Utils.DeepCopy(def.defaultMetadata)
                    }
                    -- merge provided metadata into default
                    for k, v in pairs(metadata) do newItem.metadata[k] = v end
                    inv.items[id] = newItem
                    saveItem(inv.id, newItem)
                    placed = true
                    quantity = quantity - qty
                    break
                end
            end
            if placed then break end
        end
        if not placed then
            -- no space
            return false
        end
    end
    return true
end
exports('AddItem', AddItem)

-- Remove an item from the player’s inventory. If quantity is provided
-- only reduce that amount; if the stack reaches zero the item is
-- deleted. Returns true on success.
function RemoveItem(playerSource, itemId, quantity)
    local src = playerSource
    local inv = loadInventory(getIdentifier(src))
    local item = inv.items[itemId]
    if not item then return false end
    quantity = quantity or item.quantity
    if quantity >= item.quantity then
        inv.items[itemId] = nil
        deleteItem(inv.id, itemId)
    else
        item.quantity = item.quantity - quantity
        saveItem(inv.id, item)
    end
    return true
end
exports('RemoveItem', RemoveItem)

-- Get the serialised inventory for a player. Returns a table suitable
-- for sending to the client. Note that we copy the inventory each time
-- so modifications on the client don’t affect the server state.
function GetInventory(playerSource)
    local inv = loadInventory(getIdentifier(playerSource))
    return serialiseInventory(inv)
end
exports('GetInventory', GetInventory)

-- Open a container for the player. Containers can be bags, trunks,
-- gloveboxes etc. For simplicity this implementation treats
-- containers as separate inventories with an owner string equal to
-- their unique identifier. You can extend this to restrict access
-- based on job, keys or distance. The returned inventory id can
-- optionally be stored on the client to allow closing later.
function OpenContainer(playerSource, containerId, width, height)
    local owner = ('container:%s'):format(containerId)
    local inv = loadInventory(owner, 'container')
    inv.width = width or inv.width
    inv.height = height or inv.height
    return serialiseInventory(inv)
end
exports('OpenContainer', OpenContainer)

-- Give the player their framework defined loadout. For ESX this
-- registers loadout weapons; for QBCore and Qbox we add items into
-- weapon slots. This export can be called from other scripts once the
-- inventory has been initialised.
function GiveLoadout(playerSource)
    local src = playerSource
    if Framework == 'esx' then
        local xPlayer = FW.GetPlayerFromId(src)
        if not xPlayer then return end
        for _, weapon in ipairs(xPlayer.getLoadout() or {}) do
            AddItem(src, weapon.name, 1, {serial = weapon.serialNumber, durability = weapon.durability})
        end
        -- Remove weapons from ESX loadout to avoid duplication
        xPlayer.clearInventory(true)
    elseif Framework == 'qbcore' or Framework == 'qbox' then
        local Player = FW.Functions.GetPlayer(src)
        if not Player then return end
        for _, weapon in pairs(Player.PlayerData.items or {}) do
            if Items[weapon.name] and Items[weapon.name].type == 'weapon' then
                AddItem(src, weapon.name, 1, {serial = weapon.info.serial, durability = weapon.info.durability})
                Player.Functions.RemoveItem(weapon.name, 1, false)
            end
        end
    end
end
exports('GiveLoadout', GiveLoadout)

-- Event handlers ------------------------------------------------------------

-- Client requests their inventory. Respond with the serialised list
-- and the hotbar mapping.
RegisterNetEvent('vantage_inventory:requestInventory', function()
    local src = source
    local inv = loadInventory(getIdentifier(src))
    TriggerClientEvent('vantage_inventory:update', src, serialiseInventory(inv), inv.hotbar)
end)

-- Hotbar quick use. The client sends the index; we look up the item
-- id in the player’s hotbar mapping and trigger its use. Actual use
-- behaviour depends on the item type and the selected framework.
RegisterNetEvent('vantage_inventory:hotbarUse', function(index)
    local src = source
    local inv = loadInventory(getIdentifier(src))
    local itemId = inv.hotbar[index]
    if not itemId then return end
    local item = inv.items[itemId]
    if not item then return end
    local def = Items[item.name]
    if not def then return end
    -- For weapons we equip them; for consumables we heal/hunger etc.
    if def.type == 'weapon' then
        -- Give weapon to player and remove from inventory. The weapon
        -- entity will be removed again when returned to inventory.
        RemoveItem(src, itemId, 1)
        GiveWeaponToPed(GetPlayerPed(src), GetHashKey(item.name), 0, false, true)
    elseif def.type == 'consumable' then
        -- Apply effects. Example: restore hunger/thirst/health
        if def.defaultMetadata.healthRestore and Framework then
            if Framework == 'esx' then
                -- Use status to heal player
                local xPlayer = FW.GetPlayerFromId(src)
                if xPlayer then xPlayer.triggerEvent('esx_status:add', 'health', def.defaultMetadata.healthRestore * 1000) end
            elseif Framework == 'qbcore' or Framework == 'qbox' then
                local Player = FW.Functions.GetPlayer(src)
                if Player then
                    TriggerClientEvent('hud:client:Notify', src, 'Consumed '..def.label)
                end
            end
        end
        RemoveItem(src, itemId, 1)
    end
end)

-- Move or rotate an item. The client sends the item id, the target
-- location, optional split amount and rotation flag. All values are
-- validated before modifications. On success the server broadcasts
-- updated inventory back to the player.
RegisterNetEvent('vantage_inventory:moveItem', function(data)
    local src = source
    if not checkRateLimit(src) then return end
    local inv = loadInventory(getIdentifier(src))
    local item = inv.items[data.itemId]
    if not item then return end
    local def = Items[item.name]
    if not def then return end
    local targetRow = tonumber(data.toRow)
    local targetCol = tonumber(data.toCol)
    local rotate = data.rotate and true or false
    local newWidth = rotate and item.height or item.width
    local newHeight = rotate and item.width or item.height
    -- Validate placement
    if not isPlacementValid(inv, item, targetRow, targetCol, newWidth, newHeight, item.id) then return end
    -- Optional stack split
    local split = tonumber(data.splitAmount) or 0
    if split > 0 and item.quantity > split and def.maxStack > 1 then
        -- create new item instance
        local newItemId = Utils.GenerateId()
        local newItem = {
            id = newItemId,
            name = item.name,
            row = targetRow,
            col = targetCol,
            width = newWidth,
            height = newHeight,
            quantity = split,
            metadata = Utils.DeepCopy(item.metadata)
        }
        -- reduce old stack
        item.quantity = item.quantity - split
        -- store new item
        inv.items[newItemId] = newItem
        saveItem(inv.id, item)
        saveItem(inv.id, newItem)
    else
        -- move/rotate existing item
        item.row = targetRow
        item.col = targetCol
        item.width = newWidth
        item.height = newHeight
        saveItem(inv.id, item)
    end
    -- Send updated inventory to client
    TriggerClientEvent('vantage_inventory:update', src, serialiseInventory(inv), inv.hotbar)
end)

-- Split stack into specified amount. Provided for explicit splitting
-- operations triggered by shift+drag; the UI may also send split
-- amounts through moveItem.
RegisterNetEvent('vantage_inventory:splitStack', function(data)
    local src = source
    if not checkRateLimit(src) then return end
    local inv = loadInventory(getIdentifier(src))
    local item = inv.items[data.itemId]
    if not item then return end
    local amount = tonumber(data.amount) or 1
    if amount >= item.quantity or amount <= 0 then return end
    local def = Items[item.name]
    -- find a free slot
    for r = 1, inv.height do
        for c = 1, inv.width do
            if isPlacementValid(inv, nil, r, c, item.width, item.height, nil) then
                local newId = Utils.GenerateId()
                local newItem = {
                    id = newId,
                    name = item.name,
                    row = r,
                    col = c,
                    width = item.width,
                    height = item.height,
                    quantity = amount,
                    metadata = Utils.DeepCopy(item.metadata)
                }
                item.quantity = item.quantity - amount
                inv.items[newId] = newItem
                saveItem(inv.id, item)
                saveItem(inv.id, newItem)
                TriggerClientEvent('vantage_inventory:update', src, serialiseInventory(inv), inv.hotbar)
                return
            end
        end
    end
end)

-- Drop item on the ground. Removes quantity from the player’s
-- inventory and spawns a container entity at the player’s location
-- containing the dropped items. The container is persisted with a
-- despawn timer configured via Config.ContainerDespawn. For brevity
-- this implementation does not spawn world entities; instead it just
-- removes the item from the inventory and prints a message to the
-- server console.
RegisterNetEvent('vantage_inventory:dropItem', function(data)
    local src = source
    if not checkRateLimit(src) then return end
    local inv = loadInventory(getIdentifier(src))
    local item = inv.items[data.itemId]
    if not item then return end
    local qty = tonumber(data.amount) or item.quantity
    if qty <= 0 or qty > item.quantity then return end
    RemoveItem(src, data.itemId, qty)
    -- In a full implementation you would create a drop container and spawn
    -- an object in the world that players can pick up. Here we just log.
    if Config.DebugLogging then
        print(('Player %s dropped %sx %s at their location'):format(src, qty, item.name))
    end
    TriggerClientEvent('vantage_inventory:update', src, serialiseInventory(inv), inv.hotbar)
end)

-- Give item to nearest player. For demonstration we look for the first
-- player within a radius of 3 units; you may want to integrate
-- yourself with a proper proximity detection.
RegisterNetEvent('vantage_inventory:giveItem', function(data)
    local src = source
    if not checkRateLimit(src) then return end
    local inv = loadInventory(getIdentifier(src))
    local item = inv.items[data.itemId]
    if not item then return end
    local qty = tonumber(data.amount) or item.quantity
    if qty <= 0 or qty > item.quantity then return end
    local ped = GetPlayerPed(src)
    local coords = GetEntityCoords(ped)
    local target = nil
    for _, id in ipairs(GetPlayers()) do
        if id ~= src then
            local tp = GetPlayerPed(id)
            local tcoords = GetEntityCoords(tp)
            if #(coords - tcoords) < 3.0 then
                target = id
                break
            end
        end
    end
    if not target then return end
    -- Remove from source
    RemoveItem(src, data.itemId, qty)
    -- Add to target
    AddItem(target, item.name, qty, Utils.DeepCopy(item.metadata))
    if Config.DebugLogging then
        print(('Player %s gave %sx %s to %s'):format(src, qty, item.name, target))
    end
    TriggerClientEvent('vantage_inventory:update', src, serialiseInventory(inv), inv.hotbar)
    local tInv = loadInventory(getIdentifier(target))
    TriggerClientEvent('vantage_inventory:update', target, serialiseInventory(tInv), tInv.hotbar)
end)

-- Consume item directly from a slot when clicking in the UI. The slot
-- number corresponds to the hotbar index; we map it to the item and
-- call the same logic as hotbarUse.
RegisterNetEvent('vantage_inventory:consumeItem', function(slot)
    local src = source
    local inv = loadInventory(getIdentifier(src))
    local itemId = inv.hotbar[slot]
    if itemId then
        TriggerEvent('vantage_inventory:hotbarUse', slot)
    end
end)

-- Player dropped or disconnected; save and clear inventory from memory.
AddEventHandler('playerDropped', function()
    local src = source
    local owner = getIdentifier(src)
    local inv = Inventories[owner]
    if inv then
        -- Persist all items and hotbar
        for _, item in pairs(inv.items) do
            saveItem(inv.id, item)
        end
        saveHotbar(inv)
        Inventories[owner] = nil
    end
    RateLimit[src] = nil
end)